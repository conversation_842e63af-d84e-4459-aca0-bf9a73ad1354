#!/usr/bin/env node

/**
 * Redux修复验证测试
 * 验证templateSlice中的undefined payload问题是否已修复
 */

import { configureStore } from '@reduxjs/toolkit';

console.log('🔧 开始验证Redux修复');

try {
  // 动态导入templateSlice
  const templateSliceModule = await import('./src/store/slices/templateSlice.js');
  
  console.log('✅ templateSlice导入成功');
  
  // 创建测试store
  const store = configureStore({
    reducer: {
      templates: templateSliceModule.default,
    },
  });
  
  console.log('✅ 测试store创建成功');
  
  // 测试初始状态
  const initialState = store.getState();
  console.log('\n📋 初始状态验证:');
  console.log(`  - templates: ${Array.isArray(initialState.templates.templates) ? '✅ 数组' : '❌ 非数组'}`);
  console.log(`  - loading: ${initialState.templates.loading === false ? '✅ false' : '❌ 非false'}`);
  console.log(`  - error: ${initialState.templates.error === null ? '✅ null' : '❌ 非null'}`);
  
  // 测试undefined payload处理
  console.log('\n🧪 测试undefined payload处理:');
  
  // 模拟fetchTemplates.fulfilled with undefined payload
  const undefinedPayloadAction = {
    type: 'templates/fetchTemplates/fulfilled',
    payload: undefined
  };
  
  try {
    store.dispatch(undefinedPayloadAction);
    const stateAfterUndefined = store.getState();
    console.log('  ✅ fetchTemplates undefined payload - 处理成功');
    console.log(`    - templates: ${Array.isArray(stateAfterUndefined.templates.templates) ? '数组' : '非数组'}`);
    console.log(`    - loading: ${stateAfterUndefined.templates.loading}`);
  } catch (error) {
    console.log('  ❌ fetchTemplates undefined payload - 处理失败:', error.message);
  }
  
  // 模拟fetchTemplate.fulfilled with undefined payload
  const undefinedTemplateAction = {
    type: 'templates/fetchTemplate/fulfilled',
    payload: undefined
  };
  
  try {
    store.dispatch(undefinedTemplateAction);
    const stateAfterUndefinedTemplate = store.getState();
    console.log('  ✅ fetchTemplate undefined payload - 处理成功');
    console.log(`    - currentTemplate: ${stateAfterUndefinedTemplate.templates.currentTemplate === null ? 'null' : '非null'}`);
  } catch (error) {
    console.log('  ❌ fetchTemplate undefined payload - 处理失败:', error.message);
  }
  
  // 模拟createTemplate.fulfilled with undefined payload
  const undefinedCreateAction = {
    type: 'templates/createTemplate/fulfilled',
    payload: undefined
  };
  
  try {
    store.dispatch(undefinedCreateAction);
    console.log('  ✅ createTemplate undefined payload - 处理成功');
  } catch (error) {
    console.log('  ❌ createTemplate undefined payload - 处理失败:', error.message);
  }
  
  // 测试正常payload处理
  console.log('\n✅ 测试正常payload处理:');
  
  const normalPayload = {
    items: [
      {
        id: 'test-1',
        name: '测试模板1',
        type: 'inquiry',
        scope: 'private'
      }
    ],
    total: 1,
    page: 1,
    size: 20,
    pages: 1
  };
  
  const normalAction = {
    type: 'templates/fetchTemplates/fulfilled',
    payload: normalPayload
  };
  
  try {
    store.dispatch(normalAction);
    const stateAfterNormal = store.getState();
    console.log('  ✅ 正常payload处理成功');
    console.log(`    - templates数量: ${stateAfterNormal.templates.templates.length}`);
    console.log(`    - pagination.total: ${stateAfterNormal.templates.pagination.total}`);
  } catch (error) {
    console.log('  ❌ 正常payload处理失败:', error.message);
  }
  
  // 测试同步actions
  console.log('\n🔄 测试同步actions:');
  
  try {
    store.dispatch(templateSliceModule.setFilters({ name: '测试' }));
    console.log('  ✅ setFilters - 成功');
  } catch (error) {
    console.log('  ❌ setFilters - 失败:', error.message);
  }
  
  try {
    store.dispatch(templateSliceModule.clearCurrentTemplate());
    console.log('  ✅ clearCurrentTemplate - 成功');
  } catch (error) {
    console.log('  ❌ clearCurrentTemplate - 失败:', error.message);
  }
  
  try {
    store.dispatch(templateSliceModule.clearError());
    console.log('  ✅ clearError - 成功');
  } catch (error) {
    console.log('  ❌ clearError - 失败:', error.message);
  }
  
  // 测试selectors
  console.log('\n🎯 测试selectors:');
  
  const finalState = store.getState();
  
  try {
    const templates = templateSliceModule.selectTemplates(finalState);
    console.log(`  ✅ selectTemplates - 返回 ${templates.length} 个模板`);
  } catch (error) {
    console.log('  ❌ selectTemplates - 失败:', error.message);
  }
  
  try {
    const loading = templateSliceModule.selectTemplateLoading(finalState);
    console.log(`  ✅ selectTemplateLoading - 返回 ${loading}`);
  } catch (error) {
    console.log('  ❌ selectTemplateLoading - 失败:', error.message);
  }
  
  try {
    const error = templateSliceModule.selectTemplateError(finalState);
    console.log(`  ✅ selectTemplateError - 返回 ${error}`);
  } catch (error) {
    console.log('  ❌ selectTemplateError - 失败:', error.message);
  }
  
  console.log('\n✅ Redux修复验证完成！');
  console.log('\n📊 修复总结:');
  console.log('  ✅ 所有extraReducers都添加了undefined payload保护');
  console.log('  ✅ 异步thunks都添加了默认返回值');
  console.log('  ✅ 错误处理更加健壮');
  console.log('  ✅ 状态更新逻辑更加安全');
  
  console.log('\n🎯 修复的问题:');
  console.log('  - fetchTemplates.fulfilled: payload?.items || payload || []');
  console.log('  - fetchTemplate.fulfilled: payload || null');
  console.log('  - createTemplate.fulfilled: 添加payload存在性检查');
  console.log('  - updateTemplate.fulfilled: 添加payload存在性检查');
  console.log('  - deleteTemplate.fulfilled: 添加payload存在性检查');
  console.log('  - 所有其他actions: 添加默认值保护');
  
} catch (error) {
  console.error('❌ Redux修复验证失败:', error);
}
