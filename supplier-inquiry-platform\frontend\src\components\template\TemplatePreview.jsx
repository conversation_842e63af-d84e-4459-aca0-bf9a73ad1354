import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Tag,
  Divider,
  Typography,
  Space,
  Button,
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  InputNumber,
  Checkbox,
  Radio,
  Alert,
  Row,
  Col,
  Table
} from 'antd';
import {
  EyeOutlined,
  CodeOutlined,
  FormOutlined,
  HistoryOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const TemplatePreview = ({ 
  template, 
  mode = 'preview', // 'preview' | 'form' | 'json'
  onModeChange,
  showHeader = true,
  showActions = true
}) => {
  const [previewMode, setPreviewMode] = useState(mode);
  const [form] = Form.useForm();

  useEffect(() => {
    setPreviewMode(mode);
  }, [mode]);

  const handleModeChange = (newMode) => {
    setPreviewMode(newMode);
    if (onModeChange) {
      onModeChange(newMode);
    }
  };

  // 渲染模板基本信息
  const renderTemplateInfo = () => (
    <Descriptions column={2} size="small">
      <Descriptions.Item label="模板名称">{template.name}</Descriptions.Item>
      <Descriptions.Item label="模板类型">
        <Tag color={getTypeColor(template.type)}>
          {getTypeText(template.type)}
        </Tag>
      </Descriptions.Item>
      <Descriptions.Item label="版本">v{template.version}</Descriptions.Item>
      <Descriptions.Item label="权限范围">
        <Tag color={getScopeColor(template.scope)}>
          {getScopeText(template.scope)}
        </Tag>
      </Descriptions.Item>
      <Descriptions.Item label="分类">{template.category || '-'}</Descriptions.Item>
      <Descriptions.Item label="创建时间">
        {new Date(template.created_at).toLocaleString()}
      </Descriptions.Item>
      <Descriptions.Item label="描述" span={2}>
        {template.description || '-'}
      </Descriptions.Item>
      {template.tags && template.tags.length > 0 && (
        <Descriptions.Item label="标签" span={2}>
          <Space wrap>
            {template.tags.map(tag => (
              <Tag key={tag} color="blue">{tag}</Tag>
            ))}
          </Space>
        </Descriptions.Item>
      )}
    </Descriptions>
  );

  // 渲染表单预览
  const renderFormPreview = () => {
    if (!template.content) return <Alert message="模板内容为空" type="warning" />;

    try {
      const content = typeof template.content === 'string' 
        ? JSON.parse(template.content) 
        : template.content;

      if (template.type === 'inquiry') {
        return renderInquiryForm(content);
      } else if (template.type === 'task') {
        return renderTaskForm(content);
      } else if (template.type === 'field') {
        return renderFieldPreview(content);
      }
    } catch (error) {
      return <Alert message="模板内容格式错误" type="error" />;
    }
  };

  // 渲染询价表单
  const renderInquiryForm = (content) => {
    if (!content.fields || !Array.isArray(content.fields)) {
      return <Alert message="询价模板格式错误：缺少fields数组" type="error" />;
    }

    return (
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          {content.fields.map((field, index) => (
            <Col span={12} key={index}>
              <Form.Item
                label={field.name}
                name={field.name}
                rules={field.required ? [{ required: true, message: `请输入${field.name}` }] : []}
              >
                {renderFormField(field)}
              </Form.Item>
            </Col>
          ))}
        </Row>
      </Form>
    );
  };

  // 渲染任务表单
  const renderTaskForm = (content) => {
    if (!content.fields || typeof content.fields !== 'object') {
      return <Alert message="任务模板格式错误：缺少fields对象" type="error" />;
    }

    const columns = [
      {
        title: '字段名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '默认值',
        dataIndex: 'value',
        key: 'value',
        render: (value) => {
          if (value === null || value === undefined) return '-';
          if (typeof value === 'object') return JSON.stringify(value);
          return String(value);
        }
      },
      {
        title: '数据类型',
        dataIndex: 'type',
        key: 'type',
        render: (_, record) => {
          const type = typeof record.value;
          return <Tag color="geekblue">{type}</Tag>;
        }
      }
    ];

    const dataSource = Object.entries(content.fields).map(([key, value], index) => ({
      key: index,
      name: key,
      value: value
    }));

    return <Table columns={columns} dataSource={dataSource} pagination={false} size="small" />;
  };

  // 渲染字段预览
  const renderFieldPreview = (content) => {
    return (
      <Descriptions column={1} size="small" bordered>
        <Descriptions.Item label="字段名称">{content.name || '-'}</Descriptions.Item>
        <Descriptions.Item label="字段类型">
          <Tag color="green">{content.type || '-'}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="是否必填">
          {content.required ? <Tag color="red">必填</Tag> : <Tag>选填</Tag>}
        </Descriptions.Item>
        <Descriptions.Item label="占位符">{content.placeholder || '-'}</Descriptions.Item>
        {content.options && content.options.length > 0 && (
          <Descriptions.Item label="选项">
            <Space wrap>
              {content.options.map((option, index) => (
                <Tag key={index}>{option}</Tag>
              ))}
            </Space>
          </Descriptions.Item>
        )}
        {content.validation && (
          <Descriptions.Item label="验证规则">
            <pre style={{ fontSize: '12px', margin: 0 }}>
              {JSON.stringify(content.validation, null, 2)}
            </pre>
          </Descriptions.Item>
        )}
      </Descriptions>
    );
  };

  // 渲染表单字段
  const renderFormField = (field) => {
    switch (field.type) {
      case 'string':
        return <Input placeholder={field.placeholder} />;
      case 'textarea':
        return <TextArea rows={3} placeholder={field.placeholder} />;
      case 'number':
        return <InputNumber style={{ width: '100%' }} placeholder={field.placeholder} />;
      case 'boolean':
        return <Switch />;
      case 'date':
        return <DatePicker style={{ width: '100%' }} placeholder={field.placeholder} />;
      case 'select':
        return (
          <Select placeholder={field.placeholder}>
            {field.options?.map(option => (
              <Option key={option} value={option}>{option}</Option>
            ))}
          </Select>
        );
      case 'multiselect':
        return (
          <Select mode="multiple" placeholder={field.placeholder}>
            {field.options?.map(option => (
              <Option key={option} value={option}>{option}</Option>
            ))}
          </Select>
        );
      case 'radio':
        return (
          <Radio.Group>
            {field.options?.map(option => (
              <Radio key={option} value={option}>{option}</Radio>
            ))}
          </Radio.Group>
        );
      case 'checkbox':
        return (
          <Checkbox.Group>
            {field.options?.map(option => (
              <Checkbox key={option} value={option}>{option}</Checkbox>
            ))}
          </Checkbox.Group>
        );
      default:
        return <Input placeholder={field.placeholder} />;
    }
  };

  // 渲染JSON视图
  const renderJsonView = () => (
    <pre style={{
      background: '#f5f5f5',
      padding: '16px',
      borderRadius: '4px',
      fontSize: '12px',
      lineHeight: '1.5',
      overflow: 'auto',
      maxHeight: '500px'
    }}>
      {JSON.stringify(template.content, null, 2)}
    </pre>
  );

  // 获取类型颜色
  const getTypeColor = (type) => {
    const colors = {
      inquiry: 'blue',
      task: 'green',
      field: 'orange'
    };
    return colors[type] || 'default';
  };

  // 获取类型文本
  const getTypeText = (type) => {
    const texts = {
      inquiry: '询价模板',
      task: '任务模板',
      field: '字段模板'
    };
    return texts[type] || type;
  };

  // 获取范围颜色
  const getScopeColor = (scope) => {
    const colors = {
      private: 'red',
      department: 'orange',
      company: 'blue',
      public: 'green'
    };
    return colors[scope] || 'default';
  };

  // 获取范围文本
  const getScopeText = (scope) => {
    const texts = {
      private: '私有',
      department: '部门',
      company: '公司',
      public: '公开'
    };
    return texts[scope] || scope;
  };

  if (!template) {
    return <Alert message="模板数据为空" type="warning" />;
  }

  return (
    <div className="template-preview">
      {showHeader && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={4} style={{ margin: 0 }}>
              <EyeOutlined style={{ marginRight: 8 }} />
              模板预览
            </Title>
            {showActions && (
              <Space>
                <Button
                  type={previewMode === 'preview' ? 'primary' : 'default'}
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleModeChange('preview')}
                >
                  预览
                </Button>
                <Button
                  type={previewMode === 'form' ? 'primary' : 'default'}
                  size="small"
                  icon={<FormOutlined />}
                  onClick={() => handleModeChange('form')}
                >
                  表单
                </Button>
                <Button
                  type={previewMode === 'json' ? 'primary' : 'default'}
                  size="small"
                  icon={<CodeOutlined />}
                  onClick={() => handleModeChange('json')}
                >
                  JSON
                </Button>
              </Space>
            )}
          </div>
        </Card>
      )}

      <Card>
        {previewMode === 'preview' && (
          <>
            {renderTemplateInfo()}
            <Divider />
            <Title level={5}>内容预览</Title>
            {renderFormPreview()}
          </>
        )}
        
        {previewMode === 'form' && (
          <>
            <Title level={5}>表单预览</Title>
            {renderFormPreview()}
          </>
        )}
        
        {previewMode === 'json' && (
          <>
            <Title level={5}>JSON内容</Title>
            {renderJsonView()}
          </>
        )}
      </Card>
    </div>
  );
};

export default TemplatePreview;
