import uuid
from sqlalchemy import Column, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Boolean, JSON, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.models.base import BaseModel


class TemplateVersion(BaseModel):
    """模板版本模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(UUID(as_uuid=True), ForeignKey("template.id"), nullable=False)
    version = Column(Integer, nullable=False)
    content = Column(JSON, nullable=False)
    change_description = Column(Text)
    creator_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    template = relationship("Template", back_populates="versions")
    creator = relationship("User")

    def __repr__(self):
        return f"<TemplateVersion(id={self.id}, template_id={self.template_id}, version={self.version})>"
