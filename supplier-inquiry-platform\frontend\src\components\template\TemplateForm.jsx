import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Card,
  Divider,
  Tag,
  message,
  Switch,
  Tabs,
  Alert,
  AutoComplete,
  Modal,
  Tooltip,
  Row,
  Col,
  Typography
} from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  TagsOutlined,
  QuestionCircleOutlined,
  CopyOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import JsonEditor from '../common/JsonEditor';
import {
  validateTemplateForm,
  validateTemplateName,
  formatTags,
  generateDefaultContent
} from '../../utils/validation';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const TemplateForm = ({
  initialValues,
  onSubmit,
  onSaveDraft,
  onPreview,
  loading = false,
  mode = 'create' // 'create' | 'edit'
}) => {
  const [form] = Form.useForm();
  const [templateType, setTemplateType] = useState('inquiry');
  const [templateContent, setTemplateContent] = useState('');
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState('');
  const [availableTags, setAvailableTags] = useState([
    '询价模板', '任务模板', '字段模板', '通用', '采购', '销售', '库存', '财务'
  ]);
  const [activeTab, setActiveTab] = useState('basic');
  const [isDraft, setIsDraft] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [helpVisible, setHelpVisible] = useState(false);
  const [templateExamples, setTemplateExamples] = useState({});
  const [validationErrors, setValidationErrors] = useState({});

  // 初始化模板示例
  useEffect(() => {
    setTemplateExamples({
      inquiry: {
        name: '标准询价模板',
        description: '用于一般商品询价的标准模板',
        content: {
          fields: [
            { name: '商品名称', type: 'string', required: true, placeholder: '请输入商品名称' },
            { name: '规格型号', type: 'string', required: false, placeholder: '请输入规格型号' },
            { name: '数量', type: 'number', required: true, placeholder: '请输入数量' },
            { name: '单位', type: 'select', required: false, options: ['个', '件', '套', '台', '箱'] },
            { name: '期望价格', type: 'number', required: false, placeholder: '请输入期望价格' },
            { name: '交货期', type: 'date', required: false, placeholder: '请选择交货期' },
            { name: '备注', type: 'textarea', required: false, placeholder: '其他要求或说明' }
          ]
        }
      },
      task: {
        name: '标准任务模板',
        description: '用于创建标准任务的模板',
        content: {
          fields: {
            '商品条码': '',
            '商品名称': '',
            '规格型号': '',
            '数量': 0,
            '单价': 0,
            '总价': 0,
            '供应商': '',
            '联系方式': '',
            '交货期': null,
            '质量要求': '',
            '备注': ''
          }
        }
      },
      field: {
        name: '标准字段模板',
        description: '用于定义表单字段的模板',
        content: {
          name: '示例字段',
          type: 'string',
          required: false,
          placeholder: '请输入内容',
          validation: {
            minLength: 0,
            maxLength: 100,
            pattern: ''
          },
          options: []
        }
      }
    });
  }, []);

  // 初始化表单数据
  useEffect(() => {
    if (initialValues) {
      const values = {
        ...initialValues,
        tags: initialValues.tags || []
      };
      form.setFieldsValue(values);
      setTemplateType(initialValues.type || 'inquiry');
      setTags(initialValues.tags || []);
      setTemplateContent(
        typeof initialValues.content === 'string'
          ? initialValues.content
          : JSON.stringify(initialValues.content || generateDefaultContent(initialValues.type), null, 2)
      );
    } else {
      // 新建模板时设置默认值
      const defaultContent = generateDefaultContent('inquiry');
      setTemplateContent(JSON.stringify(defaultContent, null, 2));
      form.setFieldsValue({
        type: 'inquiry',
        scope: 'private',
        is_active: true
      });
    }
  }, [initialValues, form]);

  // 处理模板类型变化
  const handleTypeChange = (type) => {
    setTemplateType(type);
    const defaultContent = generateDefaultContent(type);
    setTemplateContent(JSON.stringify(defaultContent, null, 2));
    form.setFieldsValue({ content: defaultContent });
  };

  // 处理内容编辑器变化
  const handleContentChange = (content) => {
    setTemplateContent(typeof content === 'string' ? content : JSON.stringify(content, null, 2));
    form.setFieldsValue({ content });
  };

  // 处理标签输入
  const handleTagInputChange = (value) => {
    setTagInput(value);
  };

  // 添加标签
  const handleAddTag = (tag) => {
    if (tag && !tags.includes(tag) && tags.length < 10) {
      const newTags = [...tags, tag];
      setTags(newTags);
      form.setFieldsValue({ tags: newTags });
      setTagInput('');
    }
  };

  // 删除标签
  const handleRemoveTag = (removedTag) => {
    const newTags = tags.filter(tag => tag !== removedTag);
    setTags(newTags);
    form.setFieldsValue({ tags: newTags });
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      // 验证表单数据
      const formData = {
        ...values,
        tags,
        content: templateContent
      };

      const validation = validateTemplateForm(formData);
      if (!validation.isValid) {
        Object.keys(validation.errors).forEach(field => {
          message.error(validation.errors[field]);
        });
        return;
      }

      // 提交表单
      await onSubmit(formData);
      message.success(mode === 'create' ? '模板创建成功' : '模板更新成功');
    } catch (error) {
      message.error(error.message || '操作失败');
    }
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      const values = await form.getFieldsValue();
      const draftData = {
        ...values,
        tags,
        content: templateContent,
        is_active: false
      };

      if (onSaveDraft) {
        await onSaveDraft(draftData);
        setIsDraft(true);
        message.success('草稿保存成功');
      }
    } catch (error) {
      message.error('保存草稿失败');
    }
  };

  // 预览模板
  const handlePreview = () => {
    try {
      const values = form.getFieldsValue();
      const previewData = {
        ...values,
        tags,
        content: templateContent
      };

      if (onPreview) {
        onPreview(previewData);
      } else {
        setPreviewVisible(true);
      }
    } catch (error) {
      message.error('预览失败');
    }
  };

  // 使用模板示例
  const handleUseExample = (type) => {
    const example = templateExamples[type];
    if (example) {
      const contentStr = JSON.stringify(example.content, null, 2);
      setTemplateContent(contentStr);
      form.setFieldsValue({
        name: example.name,
        description: example.description,
        content: example.content
      });
      message.success('已加载示例模板');
    }
  };

  // 实时验证
  const handleFieldChange = (changedFields, allFields) => {
    const errors = {};
    changedFields.forEach(field => {
      const { name, value } = field;
      if (name[0] === 'name') {
        const nameError = validateTemplateName(value);
        if (nameError) errors.name = nameError;
      }
      // 可以添加更多字段的实时验证
    });
    setValidationErrors(errors);
  };

  // 复制到剪贴板
  const handleCopyContent = () => {
    navigator.clipboard.writeText(templateContent).then(() => {
      message.success('模板内容已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      onFieldsChange={handleFieldChange}
      initialValues={{
        type: 'inquiry',
        scope: 'private',
        is_active: true,
        tags: []
      }}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* 基本信息标签页 */}
        <TabPane tab="基本信息" key="basic">
          <Card
            title={
              <Space>
                <span>基本信息</span>
                <Tooltip title="查看帮助文档">
                  <Button
                    type="text"
                    size="small"
                    icon={<QuestionCircleOutlined />}
                    onClick={() => setHelpVisible(true)}
                  />
                </Tooltip>
              </Space>
            }
            size="small"
          >
            <Form.Item
              name="name"
              label="模板名称"
              validateStatus={validationErrors.name ? 'error' : ''}
              help={validationErrors.name}
              rules={[
                { required: true, message: '请输入模板名称' },
                { max: 100, message: '模板名称不能超过100个字符' }
              ]}
            >
              <Input placeholder="请输入模板名称" />
            </Form.Item>

            <Form.Item
              name="description"
              label="模板描述"
              rules={[
                { max: 500, message: '模板描述不能超过500个字符' }
              ]}
            >
              <TextArea
                rows={3}
                placeholder="请输入模板描述（可选）"
                showCount
                maxLength={500}
              />
            </Form.Item>

            <Form.Item
              name="type"
              label={
                <Space>
                  <span>模板类型</span>
                  <Tooltip title="选择模板类型后可以使用对应的示例模板">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              rules={[{ required: true, message: '请选择模板类型' }]}
            >
              <Select
                placeholder="请选择模板类型"
                onChange={handleTypeChange}
              >
                <Option value="inquiry">询价模板</Option>
                <Option value="task">任务模板</Option>
                <Option value="field">字段模板</Option>
              </Select>
            </Form.Item>

            {/* 模板示例按钮 */}
            {templateType && templateExamples[templateType] && (
              <Form.Item label="快速开始">
                <Space wrap>
                  <Button
                    type="dashed"
                    icon={<CopyOutlined />}
                    onClick={() => handleUseExample(templateType)}
                  >
                    使用{templateType === 'inquiry' ? '询价' : templateType === 'task' ? '任务' : '字段'}模板示例
                  </Button>
                  <Text type="secondary">
                    快速加载标准模板结构，您可以在此基础上进行修改
                  </Text>
                </Space>
              </Form.Item>
            )}

            <Form.Item
              name="category"
              label="模板分类"
              rules={[
                { max: 50, message: '模板分类不能超过50个字符' }
              ]}
            >
              <Input placeholder="请输入模板分类（可选）" />
            </Form.Item>
          </Card>
        </TabPane>

        {/* 权限设置标签页 */}
        <TabPane tab="权限设置" key="permissions">
          <Card title="权限设置" size="small">
            <Form.Item
              name="scope"
              label="权限范围"
              rules={[{ required: true, message: '请选择权限范围' }]}
            >
              <Select placeholder="请选择权限范围">
                <Option value="private">私有（仅自己可见）</Option>
                <Option value="department">部门（部门内可见）</Option>
                <Option value="company">公司（公司内可见）</Option>
                <Option value="public">公开（所有人可见）</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="is_active"
              label="启用状态"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="启用"
                unCheckedChildren="禁用"
                defaultChecked
              />
            </Form.Item>

            <Alert
              message="权限说明"
              description="私有模板只有创建者可以查看和编辑；部门模板部门内成员可以查看；公司模板公司内成员可以查看；公开模板所有用户都可以查看。"
              type="info"
              showIcon
              icon={<InfoCircleOutlined />}
            />
          </Card>
        </TabPane>

        {/* 标签管理标签页 */}
        <TabPane tab="标签管理" key="tags">
          <Card title="标签管理" size="small">
            <Form.Item label="模板标签">
              <Space direction="vertical" style={{ width: '100%' }}>
                <AutoComplete
                  value={tagInput}
                  options={availableTags.map(tag => ({ value: tag }))}
                  onChange={handleTagInputChange}
                  onSelect={handleAddTag}
                  onPressEnter={(e) => {
                    e.preventDefault();
                    handleAddTag(tagInput);
                  }}
                  placeholder="输入标签名称，按回车添加"
                  style={{ width: '100%' }}
                />

                <div style={{ marginTop: 8 }}>
                  {tags.map(tag => (
                    <Tag
                      key={tag}
                      closable
                      onClose={() => handleRemoveTag(tag)}
                      color="blue"
                      style={{ marginBottom: 4 }}
                    >
                      <TagsOutlined /> {tag}
                    </Tag>
                  ))}
                </div>

                <Alert
                  message="标签使用说明"
                  description="标签用于分类和搜索模板，最多可添加10个标签，每个标签不超过20个字符。"
                  type="info"
                  showIcon
                />
              </Space>
            </Form.Item>
          </Card>
        </TabPane>

        {/* 内容编辑标签页 */}
        <TabPane tab="内容编辑" key="content">
          <Card
            title={
              <Space>
                <span>模板内容</span>
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={handleCopyContent}
                  title="复制内容到剪贴板"
                />
              </Space>
            }
            size="small"
          >
            <Form.Item
              name="content"
              label={
                <Space>
                  <span>{`${templateType === 'inquiry' ? '询价' : templateType === 'task' ? '任务' : '字段'}模板内容`}</span>
                  <Tooltip title="JSON格式的模板内容定义">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              rules={[{ required: true, message: '请输入模板内容' }]}
            >
              <JsonEditor
                value={templateContent}
                onChange={handleContentChange}
                placeholder={`请输入${templateType === 'inquiry' ? '询价' : templateType === 'task' ? '任务' : '字段'}模板的JSON内容`}
                rows={15}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Alert
                  message="内容格式说明"
                  description={
                    templateType === 'inquiry'
                      ? '询价模板应包含fields数组，定义询价表单的字段结构。每个字段包含name、type、required等属性。'
                      : templateType === 'task'
                      ? '任务模板应包含fields对象，定义任务的字段和默认值。字段名作为key，默认值作为value。'
                      : '字段模板应包含name、type、required、options等字段定义。'
                  }
                  type="info"
                  showIcon
                />
              </Col>
              <Col span={12}>
                <Alert
                  message="支持的字段类型"
                  description="string(文本)、number(数字)、boolean(布尔)、date(日期)、select(单选)、multiselect(多选)、textarea(多行文本)、array(数组)"
                  type="success"
                  showIcon
                />
              </Col>
            </Row>
          </Card>
        </TabPane>
      </Tabs>

      {/* 操作按钮 */}
      <Divider />
      <Form.Item>
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SaveOutlined />}
          >
            {mode === 'create' ? '创建模板' : '更新模板'}
          </Button>

          <Button
            onClick={handleSaveDraft}
            icon={<SaveOutlined />}
          >
            保存草稿
          </Button>

          <Button
            onClick={handlePreview}
            icon={<EyeOutlined />}
          >
            预览模板
          </Button>
        </Space>
      </Form.Item>

      {isDraft && (
        <Alert
          message="草稿已保存"
          description="您的模板草稿已保存，可以稍后继续编辑。"
          type="success"
          showIcon
          closable
          style={{ marginTop: 16 }}
        />
      )}

      {/* 预览模态框 */}
      <Modal
        title="模板预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          <Title level={4}>{form.getFieldValue('name') || '未命名模板'}</Title>
          <Text type="secondary">{form.getFieldValue('description') || '暂无描述'}</Text>
          <Divider />
          <pre style={{
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '4px',
            fontSize: '12px',
            lineHeight: '1.5'
          }}>
            {templateContent}
          </pre>
        </div>
      </Modal>

      {/* 帮助模态框 */}
      <Modal
        title="模板表单帮助"
        open={helpVisible}
        onCancel={() => setHelpVisible(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setHelpVisible(false)}>
            我知道了
          </Button>
        ]}
        width={700}
      >
        <div>
          <Title level={4}>模板类型说明</Title>
          <ul>
            <li><strong>询价模板：</strong>用于创建询价表单，包含商品信息、数量、价格等字段</li>
            <li><strong>任务模板：</strong>用于创建任务表单，包含任务相关的各种字段和默认值</li>
            <li><strong>字段模板：</strong>用于定义单个字段的结构和属性</li>
          </ul>

          <Title level={4}>权限范围说明</Title>
          <ul>
            <li><strong>私有：</strong>只有创建者可以查看和使用</li>
            <li><strong>部门：</strong>部门内成员可以查看和使用</li>
            <li><strong>公司：</strong>公司内成员可以查看和使用</li>
            <li><strong>公开：</strong>所有用户都可以查看和使用</li>
          </ul>

          <Title level={4}>内容格式要求</Title>
          <p>模板内容必须是有效的JSON格式，根据不同类型有不同的结构要求：</p>
          <ul>
            <li>询价模板需要包含 fields 数组</li>
            <li>任务模板需要包含 fields 对象</li>
            <li>字段模板需要包含字段定义信息</li>
          </ul>
        </div>
      </Modal>
    </Form>
  );
};

export default TemplateForm;
