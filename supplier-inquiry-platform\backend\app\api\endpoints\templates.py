from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
import uuid
import math

from app.api import deps
from app.models.template import Template
from app.models.user import User
from app.schemas.template import (
    Template as TemplateSchema,
    TemplateCreate,
    TemplateUpdate,
    TemplateResponse,
    TemplateList,
    TemplateVersion,
    TemplateVersionCreate,
    TemplateVersionList,
    TemplateVersionComparison,
    TemplateRollbackRequest
)
from app.crud import template as crud_template
from app.crud import template_version as crud_template_version
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

def is_template_accessible(db: Session, user: User, template: Template) -> bool:
    """检查用户是否有权限访问模板"""
    # 超级管理员和总部管理员可以访问所有模板
    if user.level >= 4:
        return True

    # 模板创建者可以访问
    if template.creator_id == user.id:
        return True

    # 公开模板所有人都可以访问
    if template.scope == "public":
        return True

    # 公司级别模板：同公司用户可以访问
    if template.scope == "company" and template.company_id == user.company_id:
        return True

    # 部门级别模板：分公司负责人和部门主管可以访问
    if template.scope == "department" and template.company_id == user.company_id and user.level >= 2:
        return True

    return False

def can_modify_template(db: Session, user: User, template: Template) -> bool:
    """检查用户是否有权限修改模板"""
    # 超级管理员和总部管理员可以修改所有模板
    if user.level >= 4:
        return True

    # 模板创建者可以修改
    if template.creator_id == user.id:
        return True

    # 分公司负责人可以修改本企业模板
    if user.level >= 3 and template.company_id == user.company_id:
        return True

    return False

@router.get("/", response_model=TemplateList)
def read_templates(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    name: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    type: Optional[str] = Query(None),
    scope: Optional[str] = Query(None),
) -> Any:
    """
    获取模板列表
    支持按名称、分类、类型、范围筛选
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]获取模板列表")

    # 权限检查
    if not deps.check_permission(current_user, "template", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 获取用户可访问的模板
    templates = crud_template.get_accessible_templates(
        db=db,
        user=current_user,
        skip=skip,
        limit=limit,
        name=name,
        category=category,
        type=type
    )

    # 计算总数（这里简化处理，实际应该实现对应的count函数）
    total_templates = crud_template.get_accessible_templates(
        db=db,
        user=current_user,
        skip=0,
        limit=10000,  # 获取所有来计算总数
        name=name,
        category=category,
        type=type
    )
    total = len(total_templates)
    pages = math.ceil(total / limit) if limit > 0 else 1

    return TemplateList(
        items=templates,
        total=total,
        page=skip // limit + 1 if limit > 0 else 1,
        size=limit,
        pages=pages
    )

@router.get("/{template_id}", response_model=TemplateResponse)
def read_template(
    template_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取模板详情
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]获取模板详情: {template_id}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查访问权限
    if not is_template_accessible(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限访问此模板",
        )

    # 构建响应数据
    response_data = template.__dict__.copy()

    # 添加关联信息
    if template.creator:
        response_data["creator"] = {
            "id": str(template.creator.id),
            "username": template.creator.username,
            "name": template.creator.name
        }

    if template.company:
        response_data["company"] = {
            "id": str(template.company.id),
            "name": template.company.name
        }

    if template.parent:
        response_data["parent"] = {
            "id": str(template.parent.id),
            "name": template.parent.name,
            "version": template.parent.version
        }

    return TemplateResponse(**response_data)

@router.post("/", response_model=TemplateSchema)
def create_template(
    *,
    db: Session = Depends(deps.get_db),
    template_in: TemplateCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新模板
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]创建新模板: {template_in.name}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "create", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 如果没有指定公司ID，使用当前用户的公司ID
    if not template_in.company_id and current_user.company_id:
        template_in.company_id = current_user.company_id

    # 检查父模板是否存在且可访问
    if template_in.parent_id:
        parent_template = crud_template.get_template(db=db, template_id=template_in.parent_id)
        if not parent_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="父模板不存在",
            )
        if not is_template_accessible(db, current_user, parent_template):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问父模板",
            )

    # 创建模板
    template = crud_template.create_template(
        db=db,
        template_in=template_in,
        creator_id=current_user.id
    )

    logger.info(f"模板创建成功: ID={template.id}, 名称={template.name}, 创建者={current_user.username}")
    return template

@router.put("/{template_id}", response_model=TemplateSchema)
def update_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: uuid.UUID,
    template_in: TemplateUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新模板
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]更新模板: {template_id}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查修改权限
    if not can_modify_template(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限修改此模板",
        )

    # 检查父模板是否存在且可访问
    if template_in.parent_id:
        parent_template = crud_template.get_template(db=db, template_id=template_in.parent_id)
        if not parent_template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="父模板不存在",
            )
        if not is_template_accessible(db, current_user, parent_template):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问父模板",
            )

    # 更新模板
    template = crud_template.update_template(
        db=db,
        template=template,
        template_in=template_in
    )

    logger.info(f"模板更新成功: ID={template.id}, 名称={template.name}")
    return template

@router.delete("/{template_id}", response_model=TemplateSchema)
def delete_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: uuid.UUID,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除模板（软删除）
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]删除模板: {template_id}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "delete", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查修改权限
    if not can_modify_template(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限删除此模板",
        )

    # 软删除模板
    template = crud_template.delete_template(db=db, template=template)

    logger.info(f"模板删除成功: ID={template.id}, 名称={template.name}")
    return template

@router.get("/search/by-tags", response_model=List[TemplateSchema])
def search_templates_by_tags(
    tags: str = Query(..., description="标签列表，用逗号分隔"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    根据标签搜索模板
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]根据标签搜索模板: {tags}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 解析标签
    tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    if not tag_list:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请提供有效的标签",
        )

    # 搜索模板
    templates = crud_template.search_templates_by_tags(
        db=db,
        tags=tag_list,
        user=current_user
    )

    return templates

@router.get("/company/{company_id}", response_model=List[TemplateSchema])
def read_company_templates(
    company_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
) -> Any:
    """
    获取公司的模板列表
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]获取公司模板列表: {company_id}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    # 检查是否有权限访问该公司的模板
    if current_user.level < 4 and current_user.company_id != company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限访问该公司的模板",
        )

    # 获取公司模板
    templates = crud_template.get_templates_by_company(
        db=db,
        company_id=company_id,
        skip=skip,
        limit=limit
    )

    return templates


# 版本管理相关端点

@router.get("/{template_id}/versions", response_model=TemplateVersionList)
def get_template_versions(
    template_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
) -> Any:
    """
    获取模板的版本历史
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]获取模板版本历史: {template_id}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查访问权限
    if not is_template_accessible(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限访问此模板",
        )

    # 获取版本列表
    versions = crud_template_version.get_template_versions(
        db=db, template_id=template_id, skip=skip, limit=limit
    )

    # 获取总数
    total = crud_template_version.get_template_versions_count(db=db, template_id=template_id)

    # 构建响应数据，包含创建者信息
    version_list = []
    for version in versions:
        version_data = version.__dict__.copy()
        if version.creator:
            version_data["creator"] = {
                "id": str(version.creator.id),
                "username": version.creator.username,
                "name": version.creator.name
            }
        version_list.append(TemplateVersion(**version_data))

    return TemplateVersionList(
        items=version_list,
        total=total,
        current_version=template.version
    )


@router.post("/{template_id}/versions", response_model=TemplateVersion)
def create_template_version(
    template_id: uuid.UUID,
    *,
    db: Session = Depends(deps.get_db),
    version_in: TemplateVersionCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建模板新版本
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]创建模板版本: {template_id}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查修改权限
    if not can_modify_template(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限修改此模板",
        )

    # 创建新版本
    version = crud_template_version.create_template_version(
        db=db,
        template_id=template_id,
        content=version_in.content,
        creator_id=current_user.id,
        change_description=version_in.change_description
    )

    logger.info(f"模板版本创建成功: 模板ID={template_id}, 版本={version.version}")
    return version


@router.get("/{template_id}/versions/{version}/compare/{target_version}", response_model=TemplateVersionComparison)
def compare_template_versions(
    template_id: uuid.UUID,
    version: int,
    target_version: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    比较模板的两个版本
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]比较模板版本: {template_id}, v{version} vs v{target_version}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查访问权限
    if not is_template_accessible(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限访问此模板",
        )

    # 比较版本
    comparison = crud_template_version.compare_template_versions(
        db=db, template_id=template_id, version_a=version, version_b=target_version
    )

    if "error" in comparison:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=comparison["error"],
        )

    return TemplateVersionComparison(**comparison)


@router.put("/{template_id}/rollback", response_model=TemplateVersion)
def rollback_template_to_version(
    template_id: uuid.UUID,
    *,
    db: Session = Depends(deps.get_db),
    rollback_request: TemplateRollbackRequest,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    回滚模板到指定版本
    """
    logger.info(f"用户[{current_user.id}|{current_user.username}]回滚模板: {template_id} 到版本 {rollback_request.target_version}")

    # 权限检查
    if not deps.check_permission(current_user, "template", "update", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作",
        )

    template = crud_template.get_template(db=db, template_id=template_id)
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模板不存在",
        )

    # 检查修改权限
    if not can_modify_template(db, current_user, template):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限修改此模板",
        )

    try:
        # 执行回滚
        version = crud_template_version.rollback_template_to_version(
            db=db,
            template_id=template_id,
            target_version=rollback_request.target_version,
            creator_id=current_user.id,
            change_description=rollback_request.change_description
        )

        logger.info(f"模板回滚成功: 模板ID={template_id}, 回滚到版本={rollback_request.target_version}, 新版本={version.version}")
        return version

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )