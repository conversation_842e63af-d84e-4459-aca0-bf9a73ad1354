from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
import uuid
import json

from app.models.template_version import TemplateVersion
from app.models.template import Template
from app.models.user import User
from app.schemas.template import TemplateVersionCreate


def create_template_version(
    db: Session, 
    template_id: uuid.UUID, 
    content: Dict[str, Any], 
    creator_id: uuid.UUID,
    change_description: Optional[str] = None
) -> TemplateVersion:
    """创建模板版本"""
    # 获取当前模板的最新版本号
    latest_version = get_latest_version_number(db, template_id)
    new_version = latest_version + 1
    
    version_data = {
        "id": uuid.uuid4(),
        "template_id": template_id,
        "version": new_version,
        "content": content,
        "change_description": change_description,
        "creator_id": creator_id
    }
    
    version = TemplateVersion(**version_data)
    db.add(version)
    
    # 同时更新模板的版本号和内容
    template = db.query(Template).filter(Template.id == template_id).first()
    if template:
        template.version = new_version
        template.content = content
        db.add(template)
    
    db.commit()
    db.refresh(version)
    return version


def get_template_versions(
    db: Session, 
    template_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100
) -> List[TemplateVersion]:
    """获取模板的版本历史"""
    return db.query(TemplateVersion).filter(
        TemplateVersion.template_id == template_id
    ).order_by(desc(TemplateVersion.version)).offset(skip).limit(limit).all()


def get_template_version(
    db: Session, 
    template_id: uuid.UUID, 
    version: int
) -> Optional[TemplateVersion]:
    """获取模板的特定版本"""
    return db.query(TemplateVersion).filter(
        and_(
            TemplateVersion.template_id == template_id,
            TemplateVersion.version == version
        )
    ).first()


def get_latest_version_number(db: Session, template_id: uuid.UUID) -> int:
    """获取模板的最新版本号"""
    latest_version = db.query(TemplateVersion).filter(
        TemplateVersion.template_id == template_id
    ).order_by(desc(TemplateVersion.version)).first()
    
    return latest_version.version if latest_version else 0


def get_template_versions_count(db: Session, template_id: uuid.UUID) -> int:
    """获取模板版本总数"""
    return db.query(TemplateVersion).filter(
        TemplateVersion.template_id == template_id
    ).count()


def compare_template_versions(
    db: Session, 
    template_id: uuid.UUID, 
    version_a: int, 
    version_b: int
) -> Dict[str, Any]:
    """比较两个模板版本的差异"""
    version_a_obj = get_template_version(db, template_id, version_a)
    version_b_obj = get_template_version(db, template_id, version_b)
    
    if not version_a_obj or not version_b_obj:
        return {"error": "版本不存在"}
    
    # 简单的差异比较（实际项目中可以使用更复杂的diff算法）
    differences = []
    
    content_a = version_a_obj.content
    content_b = version_b_obj.content
    
    # 比较JSON内容的差异
    differences = find_json_differences(content_a, content_b, "")
    
    return {
        "template_id": str(template_id),
        "version_a": version_a,
        "version_b": version_b,
        "differences": differences
    }


def find_json_differences(obj_a: Any, obj_b: Any, path: str = "") -> List[Dict[str, Any]]:
    """递归比较JSON对象的差异"""
    differences = []
    
    if type(obj_a) != type(obj_b):
        differences.append({
            "path": path,
            "type": "type_change",
            "old_value": obj_a,
            "new_value": obj_b,
            "old_type": type(obj_a).__name__,
            "new_type": type(obj_b).__name__
        })
        return differences
    
    if isinstance(obj_a, dict) and isinstance(obj_b, dict):
        # 比较字典
        all_keys = set(obj_a.keys()) | set(obj_b.keys())
        for key in all_keys:
            key_path = f"{path}.{key}" if path else key
            
            if key not in obj_a:
                differences.append({
                    "path": key_path,
                    "type": "added",
                    "new_value": obj_b[key]
                })
            elif key not in obj_b:
                differences.append({
                    "path": key_path,
                    "type": "removed",
                    "old_value": obj_a[key]
                })
            else:
                differences.extend(find_json_differences(obj_a[key], obj_b[key], key_path))
    
    elif isinstance(obj_a, list) and isinstance(obj_b, list):
        # 比较数组
        max_len = max(len(obj_a), len(obj_b))
        for i in range(max_len):
            item_path = f"{path}[{i}]"
            
            if i >= len(obj_a):
                differences.append({
                    "path": item_path,
                    "type": "added",
                    "new_value": obj_b[i]
                })
            elif i >= len(obj_b):
                differences.append({
                    "path": item_path,
                    "type": "removed",
                    "old_value": obj_a[i]
                })
            else:
                differences.extend(find_json_differences(obj_a[i], obj_b[i], item_path))
    
    else:
        # 比较基本类型
        if obj_a != obj_b:
            differences.append({
                "path": path,
                "type": "modified",
                "old_value": obj_a,
                "new_value": obj_b
            })
    
    return differences


def rollback_template_to_version(
    db: Session, 
    template_id: uuid.UUID, 
    target_version: int, 
    creator_id: uuid.UUID,
    change_description: Optional[str] = None
) -> TemplateVersion:
    """回滚模板到指定版本"""
    # 获取目标版本的内容
    target_version_obj = get_template_version(db, template_id, target_version)
    if not target_version_obj:
        raise ValueError(f"版本 {target_version} 不存在")
    
    # 创建新版本（回滚版本）
    rollback_description = change_description or f"回滚到版本 {target_version}"
    
    return create_template_version(
        db=db,
        template_id=template_id,
        content=target_version_obj.content,
        creator_id=creator_id,
        change_description=rollback_description
    )


def delete_template_version(db: Session, version_id: uuid.UUID) -> bool:
    """删除模板版本（物理删除，谨慎使用）"""
    version = db.query(TemplateVersion).filter(TemplateVersion.id == version_id).first()
    if version:
        db.delete(version)
        db.commit()
        return True
    return False
