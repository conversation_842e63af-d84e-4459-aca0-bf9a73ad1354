import React, { useState, useEffect } from 'react';
import { Input, message } from 'antd';

const { TextArea } = Input;

const JsonEditor = ({ 
  value, 
  onChange, 
  placeholder = '请输入JSON格式的内容',
  rows = 10,
  disabled = false,
  style = {}
}) => {
  const [jsonValue, setJsonValue] = useState('');
  const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    if (value) {
      try {
        // 如果value是对象，格式化为JSON字符串
        const formatted = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
        setJsonValue(formatted);
        setIsValid(true);
      } catch (error) {
        setJsonValue(value || '');
        setIsValid(false);
      }
    } else {
      setJsonValue('');
      setIsValid(true);
    }
  }, [value]);

  const handleChange = (e) => {
    const newValue = e.target.value;
    setJsonValue(newValue);

    // 验证JSON格式
    if (newValue.trim() === '') {
      setIsValid(true);
      onChange && onChange(null);
      return;
    }

    try {
      const parsed = JSON.parse(newValue);
      setIsValid(true);
      onChange && onChange(parsed);
    } catch (error) {
      setIsValid(false);
      // 仍然传递原始字符串，让父组件决定如何处理
      onChange && onChange(newValue);
    }
  };

  const handleBlur = () => {
    if (!isValid && jsonValue.trim() !== '') {
      message.error('JSON格式不正确，请检查语法');
    }
  };

  return (
    <TextArea
      value={jsonValue}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      style={{
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        fontSize: '13px',
        border: isValid ? undefined : '1px solid #ff4d4f',
        ...style
      }}
      status={isValid ? undefined : 'error'}
    />
  );
};

export default JsonEditor;
