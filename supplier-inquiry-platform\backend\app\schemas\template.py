from typing import Optional, List, Dict, Any
from pydantic import BaseModel, UUID4
from datetime import datetime

class TemplateBase(BaseModel):
    """模板基础模型"""
    name: str
    description: Optional[str] = None
    type: str  # inquiry, task, field
    category: Optional[str] = None
    tags: Optional[Dict[str, Any]] = None
    content: Dict[str, Any]
    version: int = 1
    scope: str = "private"  # private, department, company, public

class TemplateCreate(TemplateBase):
    """创建模板模型"""
    company_id: Optional[UUID4] = None
    parent_id: Optional[UUID4] = None

class TemplateUpdate(BaseModel):
    """更新模板模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[Dict[str, Any]] = None
    content: Optional[Dict[str, Any]] = None
    version: Optional[int] = None
    scope: Optional[str] = None
    company_id: Optional[UUID4] = None
    parent_id: Optional[UUID4] = None
    is_active: Optional[bool] = None

class TemplateInDBBase(TemplateBase):
    """数据库中的模板基础模型"""
    id: UUID4
    creator_id: UUID4
    company_id: Optional[UUID4] = None
    parent_id: Optional[UUID4] = None
    is_active: bool = True
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Template(TemplateInDBBase):
    """返回给API的模板模型"""
    pass

class TemplateResponse(Template):
    """模板响应模型，包含关联信息"""
    creator: Optional[Dict[str, Any]] = None
    company: Optional[Dict[str, Any]] = None
    parent: Optional[Dict[str, Any]] = None

class TemplateList(BaseModel):
    """模板列表响应模型"""
    items: List[Template]
    total: int
    page: int
    size: int
    pages: int

class TemplateVersionBase(BaseModel):
    """模板版本基础模型"""
    template_id: UUID4
    version: int
    content: Dict[str, Any]
    change_description: Optional[str] = None

class TemplateVersionCreate(TemplateVersionBase):
    """创建模板版本模型"""
    pass

class TemplateVersion(TemplateVersionBase):
    """模板版本模型"""
    id: UUID4
    creator_id: UUID4
    created_at: datetime
    creator: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

class TemplateVersionList(BaseModel):
    """模板版本列表响应模型"""
    items: List[TemplateVersion]
    total: int
    current_version: int

class TemplateVersionComparison(BaseModel):
    """模板版本比较模型"""
    template_id: UUID4
    version_a: int
    version_b: int
    differences: List[Dict[str, Any]]

class TemplateRollbackRequest(BaseModel):
    """模板回滚请求模型"""
    target_version: int
    change_description: Optional[str] = "回滚到版本 {target_version}"
