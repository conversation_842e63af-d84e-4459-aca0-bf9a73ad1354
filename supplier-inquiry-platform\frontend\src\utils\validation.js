// 模板表单验证工具函数

// 验证模板名称
export const validateTemplateName = (name) => {
  if (!name || name.trim().length === 0) {
    return '模板名称不能为空';
  }
  if (name.length > 100) {
    return '模板名称不能超过100个字符';
  }
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$/.test(name)) {
    return '模板名称只能包含中文、英文、数字、空格和常用符号';
  }
  return null;
};

// 验证模板描述
export const validateTemplateDescription = (description) => {
  if (description && description.length > 500) {
    return '模板描述不能超过500个字符';
  }
  return null;
};

// 验证模板类型
export const validateTemplateType = (type) => {
  const validTypes = ['inquiry', 'task', 'field'];
  if (!type) {
    return '请选择模板类型';
  }
  if (!validTypes.includes(type)) {
    return '无效的模板类型';
  }
  return null;
};

// 验证模板分类
export const validateTemplateCategory = (category) => {
  if (category && category.length > 50) {
    return '模板分类不能超过50个字符';
  }
  return null;
};

// 验证标签
export const validateTemplateTags = (tags) => {
  if (!Array.isArray(tags)) {
    return '标签必须是数组格式';
  }
  if (tags.length > 10) {
    return '标签数量不能超过10个';
  }
  for (const tag of tags) {
    if (typeof tag !== 'string' || tag.length > 20) {
      return '每个标签不能超过20个字符';
    }
  }
  return null;
};

// 验证权限范围
export const validateTemplateScope = (scope) => {
  const validScopes = ['private', 'department', 'company', 'public'];
  if (!scope) {
    return '请选择权限范围';
  }
  if (!validScopes.includes(scope)) {
    return '无效的权限范围';
  }
  return null;
};

// 验证模板内容
export const validateTemplateContent = (content, type) => {
  if (!content) {
    return '模板内容不能为空';
  }

  try {
    // 如果是字符串，尝试解析为JSON
    const parsedContent = typeof content === 'string' ? JSON.parse(content) : content;
    
    // 根据模板类型进行特定验证
    switch (type) {
      case 'inquiry':
        return validateInquiryContent(parsedContent);
      case 'task':
        return validateTaskContent(parsedContent);
      case 'field':
        return validateFieldContent(parsedContent);
      default:
        return null;
    }
  } catch (error) {
    return 'JSON格式不正确';
  }
};

// 验证询价模板内容
const validateInquiryContent = (content) => {
  if (!content.fields || !Array.isArray(content.fields)) {
    return '询价模板必须包含fields字段数组';
  }
  return null;
};

// 验证任务模板内容
const validateTaskContent = (content) => {
  if (!content.fields || typeof content.fields !== 'object') {
    return '任务模板必须包含fields字段对象';
  }
  return null;
};

// 验证字段模板内容
const validateFieldContent = (content) => {
  if (!content.type || !content.name) {
    return '字段模板必须包含type和name字段';
  }
  return null;
};

// 综合验证模板表单
export const validateTemplateForm = (formData) => {
  const errors = {};

  // 验证名称
  const nameError = validateTemplateName(formData.name);
  if (nameError) errors.name = nameError;

  // 验证描述
  const descError = validateTemplateDescription(formData.description);
  if (descError) errors.description = descError;

  // 验证类型
  const typeError = validateTemplateType(formData.type);
  if (typeError) errors.type = typeError;

  // 验证分类
  const categoryError = validateTemplateCategory(formData.category);
  if (categoryError) errors.category = categoryError;

  // 验证标签
  const tagsError = validateTemplateTags(formData.tags || []);
  if (tagsError) errors.tags = tagsError;

  // 验证权限范围
  const scopeError = validateTemplateScope(formData.scope);
  if (scopeError) errors.scope = scopeError;

  // 验证内容
  const contentError = validateTemplateContent(formData.content, formData.type);
  if (contentError) errors.content = contentError;

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// 格式化标签输入
export const formatTags = (input) => {
  if (typeof input === 'string') {
    return input.split(/[,，\s]+/).filter(tag => tag.trim().length > 0);
  }
  return Array.isArray(input) ? input : [];
};

// 生成默认模板内容
export const generateDefaultContent = (type) => {
  switch (type) {
    case 'inquiry':
      return {
        fields: [
          { name: '商品名称', type: 'string', required: true },
          { name: '规格型号', type: 'string', required: false },
          { name: '数量', type: 'number', required: true },
          { name: '单位', type: 'string', required: false }
        ]
      };
    case 'task':
      return {
        fields: {
          '商品条码': '',
          '商品名称': '',
          '规格型号': '',
          '数量': 0
        }
      };
    case 'field':
      return {
        name: '',
        type: 'string',
        required: false,
        options: []
      };
    default:
      return {};
  }
};
