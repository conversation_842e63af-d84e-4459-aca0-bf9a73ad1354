import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import templateService from '../../services/templateService.js';

// 异步操作
export const fetchTemplates = createAsyncThunk(
  'templates/fetchTemplates',
  async (params, { rejectWithValue }) => {
    try {
      const result = await templateService.getTemplates(params);
      return result || { items: [], total: 0, page: 1, size: 20, pages: 0 };
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '获取模板列表失败');
    }
  }
);

export const fetchTemplate = createAsyncThunk(
  'templates/fetchTemplate',
  async (id, { rejectWithValue }) => {
    try {
      const result = await templateService.getTemplate(id);
      return result || null;
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '获取模板详情失败');
    }
  }
);

export const createTemplate = createAsyncThunk(
  'templates/createTemplate',
  async (templateData, { rejectWithValue }) => {
    try {
      const result = await templateService.createTemplate(templateData);
      return result || null;
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '创建模板失败');
    }
  }
);

export const updateTemplate = createAsyncThunk(
  'templates/updateTemplate',
  async ({ id, templateData }, { rejectWithValue }) => {
    try {
      const result = await templateService.updateTemplate(id, templateData);
      return result || null;
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '更新模板失败');
    }
  }
);

export const deleteTemplate = createAsyncThunk(
  'templates/deleteTemplate',
  async (id, { rejectWithValue }) => {
    try {
      const result = await templateService.deleteTemplate(id);
      return result || id; // 返回删除的模板ID
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '删除模板失败');
    }
  }
);

export const searchTemplatesByTags = createAsyncThunk(
  'templates/searchByTags',
  async (tags, { rejectWithValue }) => {
    try {
      const result = await templateService.searchTemplatesByTags(tags);
      return result || [];
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '搜索模板失败');
    }
  }
);

export const fetchCompanyTemplates = createAsyncThunk(
  'templates/fetchCompanyTemplates',
  async ({ companyId, params }, { rejectWithValue }) => {
    try {
      const result = await templateService.getCompanyTemplates(companyId, params);
      return result || [];
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '获取公司模板失败');
    }
  }
);

export const copyTemplate = createAsyncThunk(
  'templates/copyTemplate',
  async ({ id, newName }, { rejectWithValue }) => {
    try {
      const result = await templateService.copyTemplate(id, newName);
      return result || null;
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '复制模板失败');
    }
  }
);

// 版本管理相关异步操作
export const fetchTemplateVersions = createAsyncThunk(
  'templates/fetchTemplateVersions',
  async ({ id, params = {} }, { rejectWithValue }) => {
    try {
      const result = await templateService.getTemplateVersions(id, params);
      return result || { items: [], total: 0, current_version: 1 };
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '获取版本历史失败');
    }
  }
);

export const createTemplateVersion = createAsyncThunk(
  'templates/createTemplateVersion',
  async ({ id, versionData }, { rejectWithValue }) => {
    try {
      const result = await templateService.createTemplateVersion(id, versionData);
      return result || null;
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '创建版本失败');
    }
  }
);

export const compareTemplateVersions = createAsyncThunk(
  'templates/compareTemplateVersions',
  async ({ id, versionA, versionB }, { rejectWithValue }) => {
    try {
      const result = await templateService.compareTemplateVersions(id, versionA, versionB);
      return result || { differences: [] };
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '版本比较失败');
    }
  }
);

export const rollbackTemplateToVersion = createAsyncThunk(
  'templates/rollbackTemplateToVersion',
  async ({ id, targetVersion, changeDescription }, { rejectWithValue }) => {
    try {
      const result = await templateService.rollbackTemplateToVersion(id, targetVersion, changeDescription);
      return result || null;
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || error.message || '回滚失败');
    }
  }
);

// 初始状态
const initialState = {
  // 模板列表数据
  templates: [],
  // 当前选中的模板
  currentTemplate: null,
  // 搜索结果
  searchResults: [],
  // 公司模板
  companyTemplates: [],
  // 版本管理相关
  versions: {
    items: [],
    total: 0,
    currentVersion: 1,
    loading: false,
    error: null
  },
  // 版本比较结果
  versionComparison: null,
  // 加载状态
  loading: false,
  // 错误信息
  error: null,
  // 分页信息
  pagination: {
    page: 1,
    size: 20,
    total: 0,
    pages: 0
  },
  // 筛选条件
  filters: {
    name: '',
    category: '',
    type: '',
    scope: '',
    tags: []
  },
  // 操作状态
  operations: {
    creating: false,
    updating: false,
    deleting: false,
    copying: false,
    searching: false,
    versionCreating: false,
    versionComparing: false,
    rollbacking: false
  }
};

// Slice
const templateSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {
    // 清除当前模板
    clearCurrentTemplate: (state) => {
      state.currentTemplate = null;
    },

    // 清除错误信息
    clearError: (state) => {
      state.error = null;
    },

    // 设置筛选条件
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },

    // 重置筛选条件
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },

    // 设置分页信息
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },

    // 清除搜索结果
    clearSearchResults: (state) => {
      state.searchResults = [];
    },

    // 清除公司模板
    clearCompanyTemplates: (state) => {
      state.companyTemplates = [];
    },

    // 清除版本历史
    clearVersions: (state) => {
      state.versions = {
        items: [],
        total: 0,
        currentVersion: 1,
        loading: false,
        error: null
      };
    },

    // 清除版本比较结果
    clearVersionComparison: (state) => {
      state.versionComparison = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchTemplates
      .addCase(fetchTemplates.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTemplates.fulfilled, (state, action) => {
        state.loading = false;
        const payload = action.payload || {};
        // 确保templates始终是数组
        if (payload.items && Array.isArray(payload.items)) {
          state.templates = payload.items;
        } else if (Array.isArray(payload)) {
          state.templates = payload;
        } else {
          state.templates = [];
        }

        if (payload.total !== undefined) {
          state.pagination = {
            page: payload.page || 1,
            size: payload.size || 20,
            total: payload.total || 0,
            pages: payload.pages || 0
          };
        }
      })
      .addCase(fetchTemplates.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '获取模板列表失败';
      })

      // fetchTemplate
      .addCase(fetchTemplate.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTemplate.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTemplate = action.payload || null;
      })
      .addCase(fetchTemplate.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '获取模板详情失败';
      })

      // createTemplate
      .addCase(createTemplate.pending, (state) => {
        state.operations.creating = true;
        state.error = null;
      })
      .addCase(createTemplate.fulfilled, (state, action) => {
        state.operations.creating = false;
        if (action.payload) {
          state.templates.unshift(action.payload); // 添加到列表开头
          state.currentTemplate = action.payload;
          // 更新分页总数
          state.pagination.total += 1;
        }
      })
      .addCase(createTemplate.rejected, (state, action) => {
        state.operations.creating = false;
        state.error = action.payload || '创建模板失败';
      })

      // updateTemplate
      .addCase(updateTemplate.pending, (state) => {
        state.operations.updating = true;
        state.error = null;
      })
      .addCase(updateTemplate.fulfilled, (state, action) => {
        state.operations.updating = false;
        if (action.payload) {
          state.currentTemplate = action.payload;
          // 更新列表中的模板
          const index = state.templates.findIndex(template => template.id === action.payload.id);
          if (index !== -1) {
            state.templates[index] = action.payload;
          }
        }
      })
      .addCase(updateTemplate.rejected, (state, action) => {
        state.operations.updating = false;
        state.error = action.payload || '更新模板失败';
      })

      // deleteTemplate
      .addCase(deleteTemplate.pending, (state) => {
        state.operations.deleting = true;
        state.error = null;
      })
      .addCase(deleteTemplate.fulfilled, (state, action) => {
        state.operations.deleting = false;
        const deletedId = action.payload;
        if (deletedId) {
          // 从列表中移除
          state.templates = state.templates.filter(template => template.id !== deletedId);
          // 如果删除的是当前模板，清除当前模板
          if (state.currentTemplate && state.currentTemplate.id === deletedId) {
            state.currentTemplate = null;
          }
          // 更新分页总数
          state.pagination.total = Math.max(0, state.pagination.total - 1);
        }
      })
      .addCase(deleteTemplate.rejected, (state, action) => {
        state.operations.deleting = false;
        state.error = action.payload || '删除模板失败';
      })

      // searchTemplatesByTags
      .addCase(searchTemplatesByTags.pending, (state) => {
        state.operations.searching = true;
        state.error = null;
      })
      .addCase(searchTemplatesByTags.fulfilled, (state, action) => {
        state.operations.searching = false;
        state.searchResults = action.payload || [];
      })
      .addCase(searchTemplatesByTags.rejected, (state, action) => {
        state.operations.searching = false;
        state.error = action.payload || '搜索模板失败';
      })

      // fetchCompanyTemplates
      .addCase(fetchCompanyTemplates.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCompanyTemplates.fulfilled, (state, action) => {
        state.loading = false;
        state.companyTemplates = action.payload || [];
      })
      .addCase(fetchCompanyTemplates.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || '获取公司模板失败';
      })

      // copyTemplate
      .addCase(copyTemplate.pending, (state) => {
        state.operations.copying = true;
        state.error = null;
      })
      .addCase(copyTemplate.fulfilled, (state, action) => {
        state.operations.copying = false;
        if (action.payload) {
          state.templates.unshift(action.payload); // 添加到列表开头
          state.currentTemplate = action.payload;
          // 更新分页总数
          state.pagination.total += 1;
        }
      })
      .addCase(copyTemplate.rejected, (state, action) => {
        state.operations.copying = false;
        state.error = action.payload || '复制模板失败';
      })

      // fetchTemplateVersions
      .addCase(fetchTemplateVersions.pending, (state) => {
        state.versions.loading = true;
        state.versions.error = null;
      })
      .addCase(fetchTemplateVersions.fulfilled, (state, action) => {
        state.versions.loading = false;
        const payload = action.payload || {};
        state.versions.items = payload.items || [];
        state.versions.total = payload.total || 0;
        state.versions.currentVersion = payload.current_version || 1;
      })
      .addCase(fetchTemplateVersions.rejected, (state, action) => {
        state.versions.loading = false;
        state.versions.error = action.payload || '获取版本历史失败';
      })

      // createTemplateVersion
      .addCase(createTemplateVersion.pending, (state) => {
        state.operations.versionCreating = true;
        state.error = null;
      })
      .addCase(createTemplateVersion.fulfilled, (state, action) => {
        state.operations.versionCreating = false;
        if (action.payload) {
          // 添加新版本到版本列表开头
          state.versions.items.unshift(action.payload);
          state.versions.total += 1;
          state.versions.currentVersion = action.payload.version;
          // 更新当前模板的版本号
          if (state.currentTemplate) {
            state.currentTemplate.version = action.payload.version;
            state.currentTemplate.content = action.payload.content;
          }
        }
      })
      .addCase(createTemplateVersion.rejected, (state, action) => {
        state.operations.versionCreating = false;
        state.error = action.payload || '创建版本失败';
      })

      // compareTemplateVersions
      .addCase(compareTemplateVersions.pending, (state) => {
        state.operations.versionComparing = true;
        state.error = null;
      })
      .addCase(compareTemplateVersions.fulfilled, (state, action) => {
        state.operations.versionComparing = false;
        state.versionComparison = action.payload || null;
      })
      .addCase(compareTemplateVersions.rejected, (state, action) => {
        state.operations.versionComparing = false;
        state.error = action.payload || '版本比较失败';
      })

      // rollbackTemplateToVersion
      .addCase(rollbackTemplateToVersion.pending, (state) => {
        state.operations.rollbacking = true;
        state.error = null;
      })
      .addCase(rollbackTemplateToVersion.fulfilled, (state, action) => {
        state.operations.rollbacking = false;
        if (action.payload) {
          // 添加回滚版本到版本列表开头
          state.versions.items.unshift(action.payload);
          state.versions.total += 1;
          state.versions.currentVersion = action.payload.version;
          // 更新当前模板
          if (state.currentTemplate) {
            state.currentTemplate.version = action.payload.version;
            state.currentTemplate.content = action.payload.content;
          }
          // 更新模板列表中的对应项
          const templateIndex = state.templates.findIndex(t => t.id === action.payload.template_id);
          if (templateIndex !== -1) {
            state.templates[templateIndex].version = action.payload.version;
            state.templates[templateIndex].content = action.payload.content;
          }
        }
      })
      .addCase(rollbackTemplateToVersion.rejected, (state, action) => {
        state.operations.rollbacking = false;
        state.error = action.payload || '回滚失败';
      });
  },
});

// 导出actions
export const {
  clearCurrentTemplate,
  clearError,
  setFilters,
  resetFilters,
  setPagination,
  clearSearchResults,
  clearCompanyTemplates,
  clearVersions,
  clearVersionComparison
} = templateSlice.actions;

// 选择器
export const selectTemplates = (state) => state.templates.templates;
export const selectCurrentTemplate = (state) => state.templates.currentTemplate;
export const selectTemplateLoading = (state) => state.templates.loading;
export const selectTemplateError = (state) => state.templates.error;
export const selectTemplatePagination = (state) => state.templates.pagination;
export const selectTemplateFilters = (state) => state.templates.filters;
export const selectSearchResults = (state) => state.templates.searchResults;
export const selectCompanyTemplates = (state) => state.templates.companyTemplates;
export const selectTemplateOperations = (state) => state.templates.operations;

// 版本管理相关选择器
export const selectTemplateVersions = (state) => state.templates.versions;
export const selectVersionComparison = (state) => state.templates.versionComparison;

export default templateSlice.reducer;
