import api from './api.js';
import LogService from './logService.js';

export const templateService = {
  // 获取模板列表
  getTemplates: async (params = {}) => {
    try {
      LogService.debug('正在获取模板列表', params);
      const response = await api.get('/templates', { params });
      LogService.debug(`成功获取模板列表，共 ${response.data?.items?.length || 0} 个模板`);
      return response.data;
    } catch (error) {
      LogService.error('获取模板列表失败', error);
      throw error;
    }
  },

  // 获取模板详情
  getTemplate: async (id) => {
    try {
      LogService.debug(`正在获取模板详情，ID: ${id}`);
      const response = await api.get(`/templates/${id}`);
      LogService.debug(`成功获取模板详情: ${response.data?.name}`);
      return response.data;
    } catch (error) {
      LogService.error(`获取模板详情失败，ID: ${id}`, error);
      throw error;
    }
  },

  // 创建模板
  createTemplate: async (templateData) => {
    try {
      LogService.debug('正在创建模板', { name: templateData.name, type: templateData.type });
      const response = await api.post('/templates', templateData);
      LogService.debug(`成功创建模板: ${response.data?.name}`);
      return response.data;
    } catch (error) {
      LogService.error('创建模板失败', error);
      throw error;
    }
  },

  // 更新模板
  updateTemplate: async (id, templateData) => {
    try {
      LogService.debug(`正在更新模板，ID: ${id}`, { name: templateData.name });
      const response = await api.put(`/templates/${id}`, templateData);
      LogService.debug(`成功更新模板: ${response.data?.name}`);
      return response.data;
    } catch (error) {
      LogService.error(`更新模板失败，ID: ${id}`, error);
      throw error;
    }
  },

  // 删除模板
  deleteTemplate: async (id) => {
    try {
      LogService.debug(`正在删除模板，ID: ${id}`);
      await api.delete(`/templates/${id}`);
      LogService.debug(`成功删除模板，ID: ${id}`);
      return id; // 返回删除的模板ID
    } catch (error) {
      LogService.error(`删除模板失败，ID: ${id}`, error);
      throw error;
    }
  },

  // 根据标签搜索模板
  searchTemplatesByTags: async (tags) => {
    try {
      LogService.debug('正在根据标签搜索模板', { tags });
      const response = await api.get('/templates/search/by-tags', {
        params: { tags: Array.isArray(tags) ? tags.join(',') : tags }
      });
      LogService.debug(`根据标签搜索到 ${response.data?.length || 0} 个模板`);
      return response.data;
    } catch (error) {
      LogService.error('根据标签搜索模板失败', error);
      throw error;
    }
  },

  // 获取公司模板
  getCompanyTemplates: async (companyId, params = {}) => {
    try {
      LogService.debug(`正在获取公司模板，公司ID: ${companyId}`, params);
      const response = await api.get(`/templates/company/${companyId}`, { params });
      LogService.debug(`成功获取公司模板，共 ${response.data?.length || 0} 个模板`);
      return response.data;
    } catch (error) {
      LogService.error(`获取公司模板失败，公司ID: ${companyId}`, error);
      throw error;
    }
  },

  // 复制模板
  copyTemplate: async (id, newName) => {
    try {
      LogService.debug(`正在复制模板，ID: ${id}`, { newName });
      // 先获取原模板
      const originalTemplate = await templateService.getTemplate(id);

      // 创建新模板数据
      const newTemplateData = {
        ...originalTemplate,
        name: newName || `${originalTemplate.name} - 副本`,
        parent_id: id, // 设置父模板ID
        version: 1 // 重置版本号
      };

      // 删除不需要的字段
      delete newTemplateData.id;
      delete newTemplateData.created_at;
      delete newTemplateData.updated_at;
      delete newTemplateData.creator;
      delete newTemplateData.company;
      delete newTemplateData.parent;

      const response = await templateService.createTemplate(newTemplateData);
      LogService.debug(`成功复制模板: ${response.name}`);
      return response;
    } catch (error) {
      LogService.error(`复制模板失败，ID: ${id}`, error);
      throw error;
    }
  },

  // 获取模板版本历史（如果后端支持）
  getTemplateVersions: async (id) => {
    try {
      LogService.debug(`正在获取模板版本历史，ID: ${id}`);
      // 这里可能需要根据实际后端API调整
      const response = await api.get(`/templates/${id}/versions`);
      LogService.debug(`成功获取模板版本历史，共 ${response.data?.length || 0} 个版本`);
      return response.data;
    } catch (error) {
      LogService.error(`获取模板版本历史失败，ID: ${id}`, error);
      throw error;
    }
  }
};

export default templateService;
